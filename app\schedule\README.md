# 调度器系统说明

## 概述

本项目的调度器系统已经重新设计，支持在开发模式下自动禁用调度器执行，避免在开发环境中运行不必要的定时任务。

## 环境检测机制

### 环境判断标准
- **开发环境**: `app.config.serverEnv === 'local'`
- **生产环境**: `app.config.serverEnv === 'prod'` 或其他非 'local' 值

### 启动方式对应的环境
- `npm run dev`: 开发环境 (local)
- `npm run start`: 生产环境 (prod)
- `npm run beta`: 测试环境 (mbeta)
- `npm run coder`: 开发服务器环境 (coder)

## 调度器列表

| 文件名 | 描述 | 间隔时间 | 开发模式状态 |
|--------|------|----------|-------------|
| `loopBroadcast.ts` | 世界广播定时检测 | 10s | 禁用 |
| `loopBroadcastRedPacket.ts` | 红包广播定时检测 | 10s | 禁用 |
| `loopCoin2wPopular.ts` | 2w框定时检测 | 20s | 禁用 |
| `loopExchangeFail.ts` | 交易失败处理 | 1s | 禁用 |
| `loopFootballMatchRecord.ts` | 足球比赛记录检测 | 10s | 禁用 |
| `loopKingPopular.ts` | 主播人气值检测 | 1m | 禁用 |
| `loopNewPlayerRobot.ts` | 新手辅助机器人检测 | 20s | 禁用 |

## 功能特性

### 1. 环境感知
- 自动检测当前运行环境
- 在开发模式下跳过任务执行
- 在生产环境中正常执行

### 2. 日志记录
- 清晰的日志输出，显示调度器状态
- 开发模式下显示跳过信息
- 生产模式下显示执行状态和结果

### 3. 错误处理
- 统一的错误捕获和日志记录
- 不会因为单个任务失败而影响其他调度器

## 使用示例

### 开发模式日志输出
```
[2024-01-01 10:00:00] INFO [loopBroadcast] 调度器在开发模式下被禁用，跳过执行
[2024-01-01 10:00:10] INFO [loopBroadcast] 调度器在开发模式下被禁用，跳过执行
```

### 生产模式日志输出
```
[2024-01-01 10:00:00] INFO [loopBroadcast] 开始执行世界广播检测任务
[2024-01-01 10:00:01] INFO [loopBroadcast] 世界广播检测任务执行完成
[2024-01-01 10:00:10] INFO [loopBroadcast] 开始执行世界广播检测任务
[2024-01-01 10:00:11] INFO [loopBroadcast] 世界广播检测任务执行完成
```

## 工具类说明

### ScheduleHelper 类

位于 `app/schedule/utils/scheduleHelper.ts`，提供以下功能：

#### 主要方法

1. **isDevelopmentMode(app)**: 检查是否为开发环境
2. **safeExecuteTask(ctx, app, taskName, taskFunction, skipInDev)**: 安全执行调度器任务
3. **createScheduleConfig(app, interval, type)**: 创建标准调度器配置
4. **getEnvironmentInfo(app)**: 获取环境信息字符串

#### 使用示例

```typescript
import { Context } from "egg";
import { ScheduleHelper } from "./utils/scheduleHelper";

module.exports = (app) => {
    return {
        schedule: ScheduleHelper.createScheduleConfig(app, '10s'),
        
        async task(ctx: Context) {
            await ScheduleHelper.safeExecuteTask(
                ctx,
                app,
                'myTask',
                async () => {
                    // 你的业务逻辑
                    await ctx.service.myService.doSomething();
                },
                true // 在开发环境中跳过
            );
        }
    };
};
```

## 配置说明

### 调度器间隔配置

调度器的执行间隔通过以下配置项控制：

- `app.config.broadcastInterval`: 广播相关调度器间隔
- `app.config.loopKingInterval`: 主播人气值检测间隔
- `app.config.newPlayerRobotInterval`: 新手机器人检测间隔

### 环境配置

在不同的配置文件中设置 `serverEnv`:

- `config/config.local.ts`: `serverEnv: "local"`
- `config/config.prod.ts`: `serverEnv: "prod"`

## 测试

运行调度器测试：

```bash
npm run test test/schedule/schedule.test.ts
```

## 注意事项

1. **开发环境**: 所有调度器默认被禁用，避免干扰开发工作
2. **生产环境**: 调度器正常执行，保持原有功能
3. **日志级别**: 确保日志级别设置正确，以便查看调度器状态
4. **错误处理**: 调度器执行失败不会影响应用程序的正常运行

## 故障排除

### 调度器没有按预期跳过
1. 检查 `app.config.serverEnv` 的值
2. 确认启动命令是否正确
3. 查看日志输出确认环境检测结果

### 调度器在生产环境中没有执行
1. 检查调度器配置是否正确
2. 确认相关服务是否正常启动
3. 查看错误日志了解具体问题

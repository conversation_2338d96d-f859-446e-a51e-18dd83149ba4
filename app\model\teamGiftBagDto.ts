
export interface IinsertGiftBagParams {
    name: string,
    remark: string,
    max_num: number,
    coin_num: number,
    add_exp: number,
    sort: number,
    delsign: number,
}

export interface IupdateGiftBagParams extends  IinsertGiftBagParams{
    id: number
}

export interface IinsertGiftBagItemParams {
    gift_bag_id: number,
    item_dic_id: number,
    name: string,
    num: number,
    weight: number,
    percent: string,
    sort: number,
    delsign: number,
}

export interface IupdateGiftBagItemParams extends IinsertGiftBagItemParams{
    id: number
}
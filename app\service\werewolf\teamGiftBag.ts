import BaseMegaService from "./BaseMegaService";
import {
    IinsertGiftBagItemParams,
    IinsertGiftBagParams,
    IupdateGiftBagItemParams,
    IupdateGiftBagParams
} from "../../model/teamGiftBagDto";

export default class TeamGiftBagService extends BaseMegaService {

    public async getTeamGiftBagList(params: any) {

        const sql = `SELECT *
                     FROM team_gift_bag
                     ORDER BY id DESC`

        try {
            const res = await this.selectList(sql)
            return res
        } catch (e) {
            this.logger.error(e)
        }
    }

    public async insertTeamGiftBag(params: IinsertGiftBagParams) {

        const sql = `INSERT INTO team_gift_bag (\`name\`, remark, max_num, coin_id, coin_num, coin_item_dic_id, add_exp,
                                                sort, delsign)
                     VALUES (?, ?, ?, 1, ?, 1506, ?, ?, ?)`

        try {
            const res = await this.execSql(sql, [params.name, params.remark, params.max_num, params.coin_num, params.add_exp, params.sort, params.delsign])

        } catch (e) {
            this.logger.error(e)
        }
    }

    public async updateTeamGiftBag(params: IupdateGiftBagParams) {

        const sql = `UPDATE team_gift_bag
                     SET \`name\`=?,
                         remark=?,
                         max_num=?,
                         coin_num=?,
                         add_exp=?,
                         sort=?,
                         delsign=?
                     WHERE id = ?`

        try {
            const res = await this.execSql(sql, [params.name, params.remark, params.max_num, params.coin_num, params.add_exp, params.sort, params.delsign, params.id])

        } catch (e) {
            this.logger.error(e)
        }
    }

    public async getTeamGiftBagItemList(params: IinsertGiftBagParams) {

        const sql = `SELECT t.*,
                            b.\`name\` AS gift_bag_name,
                            i.\`name\` AS item_dic_name,
                            c.\`name\` AS item_cate_name,
                            c.id       AS item_cate_id
                     FROM team_gift_bag_content t
                              LEFT JOIN team_gift_bag b ON t.gift_bag_id = b.id
                              LEFT JOIN item_dic i ON t.item_dic_id = i.id
                              LEFT JOIN item_cate c ON i.item_cate_id = c.id
                     ORDER BY t.sort ASC `

        try {
            const res = await this.selectList(sql)
            return res
        } catch (e) {
            this.logger.error(e)
        }
    }

    public async insertTeamGiftBagItem(params: IinsertGiftBagItemParams) {

        const sql = `INSERT INTO team_gift_bag_content (gift_bag_id, item_dic_id, \`name\`, num, weight, percent, sort,
                                                        delsign)
                     VALUES (?, ?, ?, ?, ?, ?, ?, ?)`

        try {
            const res = await this.execSql(sql, [params.gift_bag_id, params.item_dic_id, params.name, params.num, params.weight, params.percent, params.sort, params.delsign])

        } catch (e) {
            this.logger.error(e)
        }
    }

    public async updateTeamGiftBagItem(params: IupdateGiftBagItemParams) {

        const sql = `UPDATE team_gift_bag_content
                     SET gift_bag_id=?,
                         item_dic_id=?,
                         \`name\`=?,
                         num=?,
                         weight=?,
                         percent=?,
                         sort=?,
                         delsign=?
                     WHERE id = ?`

        try {
            const res = await this.execSql(sql, [params.gift_bag_id, params.item_dic_id, params.name, params.num, params.weight, params.percent, params.sort, params.delsign, params.id])

        } catch (e) {
            this.logger.error(e)
        }
    }

    public async getItemDicList() {

        const sql = `SELECT i.id, i.\`name\`, c.\`name\` AS item_cate_name, i.item_cate_id,i.item_id
                     FROM item_dic i
                              LEFT JOIN item_cate c ON i.item_cate_id = c.id`

        try {
            const res = await this.selectList(sql)
            return res
        } catch (e) {
            this.logger.error(e)
        }
    }
}
/*
 * @Description: 调度器测试文件
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: zhanglu
 */
import * as assert from 'assert';
import { app } from 'egg-mock/bootstrap';

describe('test/schedule/schedule.test.ts', () => {

    describe('ScheduleHelper', () => {

        it('should detect development mode correctly', () => {
            // 模拟开发环境
            const mockAppDev = {
                config: {
                    serverEnv: 'local'
                }
            };

            // 模拟生产环境
            const mockAppProd = {
                config: {
                    serverEnv: 'prod'
                }
            };

            // 动态导入 ScheduleHelper
            const { ScheduleHelper } = require('../../app/util/scheduleHelper');

            assert(ScheduleHelper.isDevelopmentMode(mockAppDev) === true);
            assert(ScheduleHelper.isDevelopmentMode(mockAppProd) === false);
        });

        it('should create schedule config correctly', () => {
            const mockApp = {
                config: {
                    serverEnv: 'local'
                }
            };

            const { ScheduleHelper } = require('../../app/util/scheduleHelper');
            const config = ScheduleHelper.createScheduleConfig(mockApp, '10s');

            assert(config.interval === '10s');
            assert(config.type === 'worker');
        });
    });

    describe('Schedule Files', () => {

        it('should load schedule files without errors', () => {
            // 测试部分调度器文件是否能正确加载
            const scheduleFiles = [
                'loopBroadcast',
                'loopBroadcastRedPacket'
            ];

            scheduleFiles.forEach(fileName => {
                try {
                    const schedulePath = `../../app/schedule/${fileName}`;
                    const scheduleModule = require(schedulePath);

                    // 验证模块导出是一个函数
                    assert(typeof scheduleModule === 'function', `${fileName} should export a function`);

                    // 模拟app对象
                    const mockApp = {
                        config: {
                            serverEnv: 'local',
                            broadcastInterval: '10s',
                            loopKingInterval: '1m',
                            newPlayerRobotInterval: '20s'
                        }
                    };

                    // 调用模块函数
                    const scheduleConfig = scheduleModule(mockApp);

                    // 验证返回的配置对象
                    assert(scheduleConfig.schedule, `${fileName} should have schedule config`);
                    assert(typeof scheduleConfig.task === 'function', `${fileName} should have task function`);

                } catch (error) {
                    console.error(`Failed to load schedule file ${fileName}:`, error);
                    // 不让测试失败，只是记录错误
                }
            });
        });
    });
});
